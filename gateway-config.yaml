spring:
  cloud:
    gateway:
      routes:
        # 用户服务路由
        - id: user-services-route
          uri: lb://user-services
          predicates:
            - Path=/userServices/**
          filters:
            - StripPrefix=1

        # 商品服务路由
        - id: goods-services-route
          uri: lb://goods-services
          predicates:
            - Path=/goodServices/**
          filters:
            - StripPrefix=1

        # 订单服务路由
        - id: order-services-route
          uri: lb://order-services
          predicates:
            - Path=/orderService/**
          filters:
            - StripPrefix=1

        # 支付服务路由
        - id: payment-services-route
          uri: lb://payment-services
          predicates:
            - Path=/paymentService/**
          filters:
            - StripPrefix=1

        # OAuth服务路由
        - id: oauth-services-route
          uri: lb://oauth-services
          predicates:
            - Path=/oauthService/**
          filters:
            - StripPrefix=1

        # 延迟服务路由
        - id: delayed-services-route
          uri: lb://delayed-services
          predicates:
            - Path=/delayedServices/**
          filters:
            - StripPrefix=1
  # Redis配置
  redis:
    host: ***********
    port: 6379
    password:
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

# 白名单配置
whitelist:
  paths:
    - /userServices/auth/login
    - /userServices/auth/captcha
    - /userServices/auth/register
    - /goodServices/api/no-auth/product/list
    - /goodServices/api/no-auth/product/detail
    - /actuator/health
    - /actuator/info

# 认证配置
authconfig:
  authSecretKey: "knet-gateway-secret-key-2025"
